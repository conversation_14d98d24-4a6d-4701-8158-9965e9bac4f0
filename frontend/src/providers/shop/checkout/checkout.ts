import gql from 'graphql-tag';
import {
	AddPaymentToOrderMutation,
	AvailableCountriesQuery,
	Country,
	EligiblePaymentMethodsQuery,
	EligibleShippingMethodsQuery,
	Order,
	PaymentInput,
	PaymentMethodQuote,
	ShippingMethodQuote,
} from '~/generated/graphql';
import { shopSdk } from '~/graphql-wrapper';

export const getAvailableCountriesQuery = async () => {
	return shopSdk
		.availableCountries({})
		.then((res: AvailableCountriesQuery) => res?.availableCountries as Country[]);
};

export const addPaymentToOrderMutation = async (
	input: PaymentInput = { method: 'standard-payment', metadata: {} }
) => {
	return shopSdk
		.addPaymentToOrder({ input })
		.then((res: AddPaymentToOrderMutation) => res.addPaymentToOrder as Order);
};

export const transitionOrderToStateMutation = async (state = 'ArrangingPayment') => {
	return shopSdk.transitionOrderToState({ state });
};

export const getEligibleShippingMethodsQuery = async () => {
	return shopSdk
		.eligibleShippingMethods()
		.then(
			(res: EligibleShippingMethodsQuery) => res.eligibleShippingMethods as ShippingMethodQuote[]
		);
};

export const getEligiblePaymentMethodsQuery = async () => {
	return shopSdk
		.eligiblePaymentMethods({})
		.then((res: EligiblePaymentMethodsQuery) => res.eligiblePaymentMethods as PaymentMethodQuote[]);
};

export const processNMIPayment = async (paymentToken:any) => {
	return addPaymentToOrderMutation({
		method: 'nmi',
		metadata: paymentToken
	});
};

export const processSezzlePayment = async () => {
	return addPaymentToOrderMutation({
		method: 'sezzle',
		metadata: {}
	});
};

gql`
	query availableCountries {
		availableCountries {
			id
			name
			code
		}
	}
`;

gql`
	query eligibleShippingMethods {
		eligibleShippingMethods {
			id
			name
			description
			metadata
			price
			priceWithTax
		}
	}
`;

gql`
	mutation addPaymentToOrder($input: PaymentInput!) {
		addPaymentToOrder(input: $input) {
			...CustomOrderDetail
			...on ErrorResult {
				errorCode
				message
			}
		}
	}
`;
