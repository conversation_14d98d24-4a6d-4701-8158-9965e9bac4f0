import { component$, QRL, useSignal, useVisibleTask$, Signal, $ } from '@qwik.dev/core'; // Added Signal type
import { getEligiblePaymentMethodsQuery } from '~/providers/shop/checkout/checkout';
import { EligiblePaymentMethods } from '~/types';
import NMI from './NMI';
import Sezzle from './Sezzle';

interface PaymentProps {
 onForward$: QRL<(orderCode: string) => void>; // Expects orderCode from payment methods
 onError$: QRL<(errorMessage: string) => void>; // For payment methods to report errors
 onProcessingChange$?: QRL<(isProcessing: boolean) => void>; // For payment methods to report processing state changes
 triggerNMISignal: Signal<number>; // Signal from parent to trigger NMI submission
 triggerSezzleSignal: Signal<number>; // Signal from parent to trigger Sezzle submission
 selectedPaymentMethod?: Signal<string>; // Signal to track selected payment method
 isDisabled?: boolean;
 hideButton?: boolean;
}

export default component$<PaymentProps>(({ onForward$, onError$, onProcessingChange$, triggerNMISignal, triggerSezzleSignal, selectedPaymentMethod: externalSelectedPaymentMethod, isDisabled, hideButton = false }) => {
	console.log('[Payment] Component rendering with props:', { isDisabled, hideButton });
	const paymentMethods = useSignal<EligiblePaymentMethods[]>();
	const internalSelectedPaymentMethod = useSignal<string>('nmi'); // Default to NMI

	// Use external signal if provided, otherwise use internal signal
	const selectedPaymentMethod = externalSelectedPaymentMethod || internalSelectedPaymentMethod;

	useVisibleTask$(async () => {
		console.log('[Payment] Loading eligible payment methods...');
		try {
			paymentMethods.value = await getEligiblePaymentMethodsQuery();
			console.log('[Payment] Payment methods loaded:', paymentMethods.value);
		} catch (error) {
			console.error('[Payment] Error loading payment methods:', error);
		}
	});

	const handlePaymentMethodChange = $((method: string) => {
		selectedPaymentMethod.value = method;
		console.log('[Payment] Payment method changed to:', method);
	});

	return (
		<div class={`flex flex-col space-y-4 ${isDisabled ? 'opacity-50 pointer-events-none' : ''}`}>
			{/* Payment Method Selection */}
			<div class="bg-white border border-gray-200 rounded-lg p-6">
				<h3 class="text-lg font-semibold text-gray-900 mb-4">Choose Payment Method</h3>

				<div class="space-y-3">
					{/* Credit Card Option */}
					<label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
						<input
							type="radio"
							name="paymentMethod"
							value="nmi"
							checked={selectedPaymentMethod.value === 'nmi'}
							onChange$={() => handlePaymentMethodChange('nmi')}
							class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
						/>
						<div class="ml-3 flex items-center justify-between w-full">
							<div class="flex items-center space-x-3">
								<div class="flex space-x-1">
									<div class="w-8 h-5 bg-blue-600 rounded-sm flex items-center justify-center">
										<span class="text-white text-xs font-bold">💳</span>
									</div>
								</div>
								<div>
									<div class="text-sm font-medium text-gray-900">Credit or Debit Card</div>
									<div class="text-xs text-gray-500">Visa, Mastercard, American Express</div>
								</div>
							</div>
						</div>
					</label>

					{/* Sezzle Option */}
					<label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
						<input
							type="radio"
							name="paymentMethod"
							value="sezzle"
							checked={selectedPaymentMethod.value === 'sezzle'}
							onChange$={() => handlePaymentMethodChange('sezzle')}
							class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
						/>
						<div class="ml-3 flex items-center justify-between w-full">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-5 bg-gradient-to-r from-purple-600 to-pink-600 rounded-sm flex items-center justify-center">
									<span class="text-white text-xs font-bold">S</span>
								</div>
								<div>
									<div class="text-sm font-medium text-gray-900">Sezzle - Buy Now, Pay Later</div>
									<div class="text-xs text-gray-500">4 interest-free payments</div>
								</div>
							</div>
						</div>
					</label>
				</div>
			</div>

			{/* Payment Forms */}
			<div class="w-full">
				{selectedPaymentMethod.value === 'nmi' && (
					<NMI
						isDisabled={isDisabled}
						onForward$={onForward$}
						onError$={onError$}
						onProcessingChange$={onProcessingChange$}
						hideButton={hideButton}
						triggerSignal={triggerNMISignal}
					/>
				)}

				{selectedPaymentMethod.value === 'sezzle' && (
					<Sezzle
						isDisabled={isDisabled}
						onForward$={onForward$}
						onError$={onError$}
						onProcessingChange$={onProcessingChange$}
						hideButton={hideButton}
						triggerSignal={triggerSezzleSignal}
					/>
				)}
			</div>
		</div>
	);
});
