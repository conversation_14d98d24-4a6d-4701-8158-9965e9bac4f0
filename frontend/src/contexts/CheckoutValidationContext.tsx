import { createContextId, useContext, useContextProvider, useStore, Slot, component$, useSignal, useComputed$ } from '@builder.io/qwik';

// Define the validation state structure
export interface CheckoutValidationState {
  // Customer validation
  isCustomerValid: boolean;
  customerErrors: {
    email?: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
  };
  
  // Address validation
  isShippingAddressValid: boolean;
  shippingAddressErrors: {
    streetLine1?: string;
    city?: string;
    province?: string;
    postalCode?: string;
    countryCode?: string;
  };
  
  // Billing address validation (when different billing is used)
  isBillingAddressValid: boolean;
  billingAddressErrors: {
    firstName?: string;
    lastName?: string;
    streetLine1?: string;
    city?: string;
    province?: string;
    postalCode?: string;
    countryCode?: string;
  };
  useDifferentBilling: boolean;
  
  // Payment validation
  isPaymentValid: boolean;
  paymentErrors: {
    cardNumber?: string;
    expiryDate?: string;
    cvv?: string;
  };
  
  // Terms & Conditions validation
  isTermsAccepted: boolean;
  
  // Overall validation state
  isAllValid: boolean;
  
  // Validation touched states to know if user has interacted with forms
  customerTouched: boolean;
  shippingAddressTouched: boolean;
  billingAddressTouched: boolean;
  paymentTouched: boolean;
}

// Context type with internal state reference
export interface CheckoutValidationContext extends CheckoutValidationState {
  _state: CheckoutValidationState;
}

// Create the context
export const CheckoutValidationContextId = createContextId<CheckoutValidationContext>('checkout-validation');

// Hook to use the context
export const useCheckoutValidation = () => useContext(CheckoutValidationContextId);

// Initial state
const createInitialState = (): CheckoutValidationState => ({
  isCustomerValid: false,
  customerErrors: {},
  isShippingAddressValid: false,
  shippingAddressErrors: {},
  isBillingAddressValid: true, // Valid by default since it's optional
  billingAddressErrors: {},
  useDifferentBilling: false,
  isPaymentValid: false,
  paymentErrors: {},
  isTermsAccepted: false,
  isAllValid: false,
  customerTouched: false,
  shippingAddressTouched: false,
  billingAddressTouched: false,
  paymentTouched: false,
});

// Provider component
export const CheckoutValidationProvider = component$(() => {
  const state = useStore<CheckoutValidationState>(createInitialState());

  // Computed signal for overall validation that automatically updates
  const isAllValid = useComputed$(() => {
    const customerValid = state.isCustomerValid;
    const shippingValid = state.isShippingAddressValid;
    const billingValid = state.useDifferentBilling ? state.isBillingAddressValid : true;
    const paymentValid = state.isPaymentValid;
    const termsValid = state.isTermsAccepted;

    const overall = customerValid && shippingValid && billingValid && paymentValid && termsValid;

    console.log('[CheckoutValidation] Overall validation recalculated:', {
      customer: customerValid,
      shipping: shippingValid,
      billing: billingValid,
      payment: paymentValid,
      terms: termsValid,
      overall
    });

    return overall;
  });

  // Create context value with only serializable data
  const contextValue = useStore({
    // State properties (reactive)
    get isCustomerValid() { return state.isCustomerValid; },
    get customerErrors() { return state.customerErrors; },
    get isShippingAddressValid() { return state.isShippingAddressValid; },
    get shippingAddressErrors() { return state.shippingAddressErrors; },
    get isBillingAddressValid() { return state.isBillingAddressValid; },
    get billingAddressErrors() { return state.billingAddressErrors; },
    get useDifferentBilling() { return state.useDifferentBilling; },
    get isPaymentValid() { return state.isPaymentValid; },
    get paymentErrors() { return state.paymentErrors; },
    get isTermsAccepted() { return state.isTermsAccepted; },
    get isAllValid() { return isAllValid.value; },
    get customerTouched() { return state.customerTouched; },
    get shippingAddressTouched() { return state.shippingAddressTouched; },
    get billingAddressTouched() { return state.billingAddressTouched; },
    get paymentTouched() { return state.paymentTouched; },

    // Store reference to the actual state for actions to modify
    _state: state
  });

  // Use Qwik's useContextProvider
  useContextProvider(CheckoutValidationContextId, contextValue);

  return <Slot />;
});

// Action functions that work with the context
export const useCheckoutValidationActions = () => {
  const context = useCheckoutValidation();

  return {
    updateCustomerValidation: (isValid: boolean, errors: CheckoutValidationState['customerErrors'], touched = true) => {
      context._state.isCustomerValid = isValid;
      context._state.customerErrors = errors;
      if (touched) context._state.customerTouched = true;
    },

    updateShippingAddressValidation: (isValid: boolean, errors: CheckoutValidationState['shippingAddressErrors'], touched = true) => {
      context._state.isShippingAddressValid = isValid;
      context._state.shippingAddressErrors = errors;
      if (touched) context._state.shippingAddressTouched = true;
    },

    updateBillingAddressValidation: (isValid: boolean, errors: CheckoutValidationState['billingAddressErrors'], touched = true) => {
      context._state.isBillingAddressValid = isValid;
      context._state.billingAddressErrors = errors;
      if (touched) context._state.billingAddressTouched = true;
    },

    updatePaymentValidation: (isValid: boolean, errors: CheckoutValidationState['paymentErrors'], touched = true) => {
      context._state.isPaymentValid = isValid;
      context._state.paymentErrors = errors;
      if (touched) context._state.paymentTouched = true;
    },

    updateBillingMode: (useDifferentBilling: boolean) => {
      context._state.useDifferentBilling = useDifferentBilling;
      // If switching to same billing, mark billing as valid
      if (!useDifferentBilling) {
        context._state.isBillingAddressValid = true;
        context._state.billingAddressErrors = {};
      }
    },

    updateTermsAcceptance: (isAccepted: boolean) => {
      context._state.isTermsAccepted = isAccepted;
    },

    resetValidation: () => {
      Object.assign(context._state, createInitialState());
    }
  };
};
