import { createContextId, useContext, useContextProvider, useStore, Slot, component$, $ } from '@builder.io/qwik';

// Define the validation state structure
export interface CheckoutValidationState {
  // Customer validation
  isCustomerValid: boolean;
  customerErrors: {
    email?: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
  };
  
  // Address validation
  isShippingAddressValid: boolean;
  shippingAddressErrors: {
    streetLine1?: string;
    city?: string;
    province?: string;
    postalCode?: string;
    countryCode?: string;
  };
  
  // Billing address validation (when different billing is used)
  isBillingAddressValid: boolean;
  billingAddressErrors: {
    firstName?: string;
    lastName?: string;
    streetLine1?: string;
    city?: string;
    province?: string;
    postalCode?: string;
    countryCode?: string;
  };
  useDifferentBilling: boolean;
  
  // Payment validation
  isPaymentValid: boolean;
  paymentErrors: {
    cardNumber?: string;
    expiryDate?: string;
    cvv?: string;
  };
  
  // Terms & Conditions validation
  isTermsAccepted: boolean;
  
  // Overall validation state
  isAllValid: boolean;
  
  // Validation touched states to know if user has interacted with forms
  customerTouched: boolean;
  shippingAddressTouched: boolean;
  billingAddressTouched: boolean;
  paymentTouched: boolean;
}

// Context only provides the state - no functions
export type CheckoutValidationContext = CheckoutValidationState;

// Create the context
export const CheckoutValidationContextId = createContextId<CheckoutValidationContext>('checkout-validation');

// Hook to use the context
export const useCheckoutValidation = () => useContext(CheckoutValidationContextId);

// Initial state
const createInitialState = (): CheckoutValidationState => ({
  isCustomerValid: false,
  customerErrors: {},
  isShippingAddressValid: false,
  shippingAddressErrors: {},
  isBillingAddressValid: true, // Valid by default since it's optional
  billingAddressErrors: {},
  useDifferentBilling: false,
  isPaymentValid: false,
  paymentErrors: {},
  isTermsAccepted: false,
  isAllValid: false,
  customerTouched: false,
  shippingAddressTouched: false,
  billingAddressTouched: false,
  paymentTouched: false,
});

// Global state store that can be accessed from anywhere
let globalValidationState: CheckoutValidationState | null = null;

// Provider component
export const CheckoutValidationProvider = component$(() => {
  const state = useStore<CheckoutValidationState>(createInitialState());

  // Store reference to global state for actions to access
  globalValidationState = state;

  // Use Qwik's useContextProvider - only provide the state
  useContextProvider(CheckoutValidationContextId, state);

  return <Slot />;
});

// Helper function to recalculate overall validation
const recalculateOverallValidation = () => {
  if (!globalValidationState) return;

  const state = globalValidationState;
  const customerValid = state.isCustomerValid;
  const shippingValid = state.isShippingAddressValid;
  const billingValid = state.useDifferentBilling ? state.isBillingAddressValid : true;
  const paymentValid = state.isPaymentValid;
  const termsValid = state.isTermsAccepted;

  state.isAllValid = customerValid && shippingValid && billingValid && paymentValid && termsValid;

  console.log('[CheckoutValidation] Overall validation recalculated:', {
    customer: customerValid,
    shipping: shippingValid,
    billing: billingValid,
    payment: paymentValid,
    terms: termsValid,
    overall: state.isAllValid
  });
};

// Action functions that can be called from anywhere
export const updateCustomerValidation = (isValid: boolean, errors: CheckoutValidationState['customerErrors'], touched = true) => {
  if (!globalValidationState) return;
  globalValidationState.isCustomerValid = isValid;
  globalValidationState.customerErrors = errors;
  if (touched) globalValidationState.customerTouched = true;
  recalculateOverallValidation();
};

export const updateShippingAddressValidation = (isValid: boolean, errors: CheckoutValidationState['shippingAddressErrors'], touched = true) => {
  if (!globalValidationState) return;
  globalValidationState.isShippingAddressValid = isValid;
  globalValidationState.shippingAddressErrors = errors;
  if (touched) globalValidationState.shippingAddressTouched = true;
  recalculateOverallValidation();
};

export const updateBillingAddressValidation = (isValid: boolean, errors: CheckoutValidationState['billingAddressErrors'], touched = true) => {
  if (!globalValidationState) return;
  globalValidationState.isBillingAddressValid = isValid;
  globalValidationState.billingAddressErrors = errors;
  if (touched) globalValidationState.billingAddressTouched = true;
  recalculateOverallValidation();
};

export const updatePaymentValidation = (isValid: boolean, errors: CheckoutValidationState['paymentErrors'], touched = true) => {
  if (!globalValidationState) return;
  globalValidationState.isPaymentValid = isValid;
  globalValidationState.paymentErrors = errors;
  if (touched) globalValidationState.paymentTouched = true;
  recalculateOverallValidation();
};

export const updateBillingMode = (useDifferentBilling: boolean) => {
  if (!globalValidationState) return;
  globalValidationState.useDifferentBilling = useDifferentBilling;
  // If switching to same billing, mark billing as valid
  if (!useDifferentBilling) {
    globalValidationState.isBillingAddressValid = true;
    globalValidationState.billingAddressErrors = {};
  }
  recalculateOverallValidation();
};

export const updateTermsAcceptance = (isAccepted: boolean) => {
  if (!globalValidationState) return;
  globalValidationState.isTermsAccepted = isAccepted;
  recalculateOverallValidation();
};

export const resetValidation = () => {
  if (!globalValidationState) return;
  Object.assign(globalValidationState, createInitialState());
};
